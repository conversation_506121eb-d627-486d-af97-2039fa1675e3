# Merai AI Assistant - Typebot Integration

A modern React-based chat interface powered by the official Typebot component (`@typebot.io/nextjs`), providing seamless conversational experiences.

## 🚀 Features

- **Official Typebot Component**: Uses `@typebot.io/nextjs` for native Typebot integration
- **Plug & Play**: Simple configuration with minimal setup required
- **Modern UI**: Clean, responsive design with dark/light theme support
- **Full Typebot Features**: Complete support for all Typebot capabilities
- **Restart Functionality**: Easy conversation restart with component remounting
- **TypeScript**: Full type safety and excellent developer experience

## 📋 Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- A Typebot account and configured bot
- Typebot server running (local or hosted)

## 🛠️ Setup

### 1. <PERSON><PERSON> and Install

```bash
git clone <your-repo-url>
cd merai-ai-assistant
npm install
```

### 2. Install Typebot Package

```bash
npm install @typebot.io/nextjs
```

### 3. Configure Environment

```bash
# Copy the example environment file
cp .env.example .env
```

Edit `.env` with your Typebot configuration:

```env
VITE_TYPEBOT_URL=http://localhost:7071
VITE_TYPEBOT_ID=typebot-merai
```

### 4. Get Your Typebot Configuration

#### Typebot ID/Name
- This is the identifier for your typebot (e.g., "typebot-merai")
- You can find this in your Typebot dashboard or when you publish your bot

#### API Host URL
- This is your Typebot server URL (e.g., "http://localhost:7071")
- For hosted Typebots, use the provided URL from your hosting service

### 5. Start Development

```bash
npm run dev
```

Visit `http://localhost:5173` to see your chat interface.

## 🏗️ Build for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## 🧪 Testing

Simply navigate to your application and test the Typebot integration directly. The official component handles all the complexity internally.

## 📁 Project Structure

```
src/
├── components/
│   ├── custom/          # Custom components
│   │   ├── header.tsx   # Application header
│   │   └── theme-toggle.tsx # Dark/light theme toggle
│   └── ui/              # UI components (buttons, etc.)
├── pages/
│   └── chat/
│       └── typebot-chat.tsx  # Main Typebot chat component
├── interfaces/
│   └── interfaces.ts    # Basic TypeScript interfaces
├── config.ts            # Typebot configuration
└── context/
    └── ThemeContext.tsx # Theme management
```

## 🔧 Configuration

All configuration is handled through environment variables:

- `VITE_TYPEBOT_URL`: Your Typebot server URL (API Host)
- `VITE_TYPEBOT_ID`: Your Typebot ID/name

## 🎨 Customization

### Styling
- Uses Tailwind CSS for styling
- Supports dark/light themes
- Responsive design for all screen sizes

### Typebot Features
The official `@typebot.io/nextjs` component provides:
- Full Typebot functionality
- All input types and interactions
- Session management
- Real-time updates
- Error handling

## 🐛 Troubleshooting

### Common Issues

1. **Configuration Errors**
   - Verify environment variables are set correctly
   - Check that your Typebot ID/name matches your bot
   - Ensure Typebot server is accessible

2. **Network Errors**
   - Confirm Typebot server is running
   - Check for CORS issues
   - Verify server URL is correct

3. **Component Not Loading**
   - Check browser console for errors
   - Verify `@typebot.io/nextjs` package is installed
   - Ensure your Typebot is published and accessible

## 📚 Documentation

- [Typebot Integration Guide](./TYPEBOT_INTEGRATION.md) - Detailed integration documentation
- [Typebot API Documentation](https://docs.typebot.io/api) - Official API reference

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
- Check the troubleshooting section
- Review the integration guide
- Open an issue in the repository

---

**Built with ❤️ using React, TypeScript, and Typebot**
