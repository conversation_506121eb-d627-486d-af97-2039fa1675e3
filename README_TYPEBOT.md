# Merai AI Assistant - Typebot Integration

A modern React-based chat interface powered by Typebot API, providing intelligent conversational experiences.

## 🚀 Features

- **Typebot Integration**: Full integration with Typebot API for intelligent conversations
- **Modern UI**: Clean, responsive design with dark/light theme support
- **Interactive Elements**: Support for buttons, rich text, and dynamic content
- **Session Management**: Automatic session handling and conversation flow
- **Error Handling**: Comprehensive error handling with user feedback
- **TypeScript**: Full type safety and excellent developer experience

## 📋 Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- A Typebot account and configured bot
- Typebot server running (local or hosted)

## 🛠️ Setup

### 1. <PERSON><PERSON> and Install

```bash
git clone <your-repo-url>
cd merai-ai-assistant
npm install
```

### 2. Configure Environment

```bash
# Copy the example environment file
cp .env.example .env
```

Edit `.env` with your Typebot credentials:

```env
VITE_TYPEBOT_URL=http://localhost:7071
VITE_TYPEBOT_ID=your_typebot_id_here
VITE_TYPEBOT_TOKEN=your_api_token_here
```

### 3. Get Your Typebot Credentials

#### Typebot ID
1. Go to your [Typebot dashboard](https://app.typebot.io/typebots)
2. Select your typebot
3. Copy the ID from the URL: `https://app.typebot.io/typebots/{TYPEBOT_ID}/...`

#### API Token
1. Navigate to Settings & Members > My account
2. Under "API tokens" section, click "Create"
3. Give it a name and click "Create token"
4. Copy the generated token

### 4. Start Development

```bash
npm run dev
```

Visit `http://localhost:5173` to see your chat interface.

## 🏗️ Build for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## 🧪 Testing

The application includes built-in testing tools. Open the browser console and use:

```javascript
// Check configuration
checkTypebotConfig()

// Test full integration
testTypebotIntegration()
```

## 📁 Project Structure

```
src/
├── components/
│   ├── custom/          # Custom components
│   │   ├── chatinput.tsx
│   │   ├── header.tsx
│   │   ├── message.tsx
│   │   └── overview.tsx
│   └── ui/              # UI components
├── pages/
│   └── chat/
│       └── typebot-chat.tsx  # Main chat component
├── services/
│   └── typebotApi.ts    # Typebot API service
├── interfaces/
│   └── interfaces.ts    # TypeScript interfaces
├── config.ts            # Configuration
└── utils/
    └── typebotTest.ts   # Testing utilities
```

## 🔧 Configuration

All configuration is handled through environment variables:

- `VITE_TYPEBOT_URL`: Your Typebot server URL
- `VITE_TYPEBOT_ID`: Your Typebot ID
- `VITE_TYPEBOT_TOKEN`: Your API authentication token

## 🎨 Customization

### Styling
- Uses Tailwind CSS for styling
- Supports dark/light themes
- Responsive design for all screen sizes

### Typebot Features
- Text messages with rich formatting
- Interactive buttons
- Custom input types
- Session persistence
- Error handling

## 🐛 Troubleshooting

### Common Issues

1. **Configuration Errors**
   - Verify environment variables are set
   - Check Typebot ID and token validity
   - Ensure Typebot server is accessible

2. **Network Errors**
   - Confirm Typebot server is running
   - Check for CORS issues
   - Verify server URL is correct

3. **Authentication Errors**
   - Regenerate API token
   - Check token permissions

### Debug Tools

Use the built-in test functions in the browser console:
- `checkTypebotConfig()` - Validate configuration
- `testTypebotIntegration()` - Full integration test

## 📚 Documentation

- [Typebot Integration Guide](./TYPEBOT_INTEGRATION.md) - Detailed integration documentation
- [Typebot API Documentation](https://docs.typebot.io/api) - Official API reference

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
- Check the troubleshooting section
- Review the integration guide
- Open an issue in the repository

---

**Built with ❤️ using React, TypeScript, and Typebot**
