# Typebot Component Integration - Complete Guide

This document explains the simplified Typebot integration using the official `@typebot.io/nextjs` component.

## 🎯 Overview

The application has been completely refactored to use the official Typebot React component instead of manual API integration. This provides:

- **Simplified Setup**: No complex API handling required
- **Official Support**: Uses the officially maintained component
- **Full Features**: Complete Typebot functionality out of the box
- **Better Reliability**: Maintained by the Typebot team

## 🚀 Quick Start

### 1. Install the Package

```bash
npm install @typebot.io/nextjs
```

### 2. Configure Environment

Create `.env` file:

```env
VITE_TYPEBOT_URL=http://localhost:7071
VITE_TYPEBOT_ID=typebot-merai
```

### 3. Use the Component

```tsx
import { Standard } from '@typebot.io/nextjs';

const TypebotChat = () => {
  return (
    <Standard
      typebot="typebot-merai"
      apiHost="http://localhost:7071"
      style={{ width: "100%", height: "600px" }}
    />
  );
};
```

## 📋 Configuration Options

### Environment Variables

- `VITE_TYPEBOT_URL`: Your Typebot server URL
- `VITE_TYPEBOT_ID`: Your Typebot identifier

### Component Props

The `Standard` component accepts:

- `typebot`: Your Typebot ID/name
- `apiHost`: Your Typebot server URL
- `style`: CSS styling object
- `key`: For component remounting (restart functionality)

## 🔧 Implementation Details

### Main Component Structure

```tsx
export function TypebotChat() {
  const [key, setKey] = useState<number>(0);

  const handleRestart = () => {
    setKey(prev => prev + 1); // Remounts component
  };

  return (
    <div className="flex flex-col h-screen">
      <Header />
      <div className="flex-1">
        <Standard
          key={key}
          typebot={TYPEBOT_CONFIG.TYPEBOT_ID}
          apiHost={TYPEBOT_CONFIG.API_HOST}
          style={TYPEBOT_CONFIG.STYLE}
        />
      </div>
    </div>
  );
}
```

### Configuration Management

```tsx
export const TYPEBOT_CONFIG = {
  TYPEBOT_ID: import.meta.env.VITE_TYPEBOT_ID || "typebot-merai",
  API_HOST: import.meta.env.VITE_TYPEBOT_URL || "http://localhost:7071",
  STYLE: {
    width: "100%",
    height: "100%",
    minHeight: "600px"
  }
};
```

## ✨ Features

### Restart Functionality

The restart button remounts the entire Typebot component by changing the `key` prop:

```tsx
const handleRestart = () => {
  setKey(prev => prev + 1);
};
```

### Responsive Design

The component is fully responsive and adapts to different screen sizes:

```css
.flex-1 {
  flex: 1 1 0%;
  width: 100%;
}
```

### Theme Support

The application maintains dark/light theme support around the Typebot component.

## 🎨 Styling

### Container Styling

```tsx
style={{
  width: "100%",
  height: "100%",
  minHeight: "600px"
}}
```

### Responsive Layout

```tsx
<div className="flex flex-col h-screen">
  <Header /> {/* Fixed header */}
  <div className="flex-1"> {/* Flexible Typebot container */}
    <Standard ... />
  </div>
</div>
```

## 🔍 Troubleshooting

### Common Issues

1. **Component Not Loading**
   - Check if `@typebot.io/nextjs` is installed
   - Verify environment variables
   - Check browser console for errors

2. **Network Issues**
   - Ensure Typebot server is running
   - Check CORS configuration
   - Verify API host URL

3. **Configuration Problems**
   - Confirm Typebot ID is correct
   - Check if Typebot is published
   - Verify server accessibility

### Debug Steps

1. Check browser console for errors
2. Verify environment variables are loaded
3. Test Typebot server directly
4. Check network requests in DevTools

## 📦 Dependencies

### Required Packages

```json
{
  "@typebot.io/nextjs": "latest",
  "react": "^18.3.1",
  "react-dom": "^18.3.1"
}
```

### Development Dependencies

```json
{
  "typescript": "~5.6.2",
  "vite": "^5.4.10",
  "tailwindcss": "^3.4.14"
}
```

## 🚀 Deployment

### Build for Production

```bash
npm run build
```

### Environment Variables

Ensure production environment has:

```env
VITE_TYPEBOT_URL=https://your-typebot-server.com
VITE_TYPEBOT_ID=your-production-typebot-id
```

## 📚 Resources

- [Typebot Documentation](https://docs.typebot.io/)
- [React Component Guide](https://docs.typebot.io/embed/react)
- [API Reference](https://docs.typebot.io/api)

## 🎉 Benefits of This Approach

1. **Simplified Codebase**: Removed complex API handling
2. **Official Support**: Uses maintained component
3. **Better Performance**: Optimized by Typebot team
4. **Full Features**: All Typebot capabilities available
5. **Easy Updates**: Component updates automatically
6. **Reduced Maintenance**: Less custom code to maintain

This integration provides a robust, maintainable, and feature-complete Typebot experience with minimal setup and configuration.
