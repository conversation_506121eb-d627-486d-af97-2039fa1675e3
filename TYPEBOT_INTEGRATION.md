# Typebot Integration Guide

This document explains how to integrate and use the Typebot API with your frontend application.

## Overview

The application now supports two chat interfaces:
1. **Regular Chat** (`/chat`) - Your existing chat system
2. **Typebot Chat** (`/typebot`) - New Typebot-powered chat interface

## Configuration

### 1. Environment Variables

Create a `.env` file in your project root with the following variables:

```env
# Typebot Configuration
VITE_TYPEBOT_URL=http://localhost:7071
VITE_TYPEBOT_ID=your_typebot_id_here
VITE_TYPEBOT_TOKEN=your_api_token_here
```

### 2. Getting Your Typebot Credentials

#### Typebot ID
1. Navigate to your Typebot dashboard (https://app.typebot.io/typebots)
2. Select your typebot
3. The ID is visible in the URL: `https://app.typebot.io/typebots/{TYPEBOT_ID}/...`

#### API Token
1. Go to Settings & Members > My account
2. Under "API tokens" section, click "Create"
3. Give it a name and click "Create token"
4. Copy the generated token

### 3. Typebot Server Setup

Make sure your Typebot server is running on the configured URL. For local development:

```bash
# Start your Typebot server (example)
# This depends on your Typebot setup
```

## Usage

### Starting a Chat

The Typebot chat automatically initializes when you navigate to `/typebot`. The system will:

1. Validate the configuration
2. Start a new chat session with your Typebot
3. Display any initial messages from the Typebot
4. Set up the appropriate input interface

### Supported Features

- **Text Messages**: Standard text-based conversation
- **Button Inputs**: Interactive buttons for user selection
- **Rich Text**: Formatted text with bold, italic, underline
- **Session Management**: Automatic session handling
- **Error Handling**: Graceful error handling with user feedback

### Navigation

Use the header navigation buttons to switch between:
- **Regular Chat**: Your existing chat system
- **Typebot Chat**: The new Typebot-powered interface

## API Integration Details

### Service Layer

The `TypebotApiService` (`src/services/typebotApi.ts`) handles:
- Authentication with Bearer tokens
- Starting new chat sessions
- Continuing existing conversations
- Message formatting and parsing
- Error handling

### Key Methods

```typescript
// Start a new chat session
await typebotApi.startChat(request);

// Continue an existing conversation
await typebotApi.continueChat(sessionId, message);

// Extract text from Typebot messages
typebotApi.extractTextFromMessages(messages);

// Check for input requirements
typebotApi.hasInput(response);
```

### Message Flow

1. **Start Chat**: POST to `/api/v1/typebots/{typebotId}/preview/startChat`
2. **Continue Chat**: POST to `/api/v1/sessions/{sessionId}/continueChat`
3. **Response Processing**: Extract messages, inputs, and actions
4. **UI Updates**: Update chat interface based on response

## Troubleshooting

### Common Issues

1. **Configuration Errors**
   - Verify your environment variables are set correctly
   - Check that your Typebot ID and token are valid
   - Ensure your Typebot server is accessible

2. **Network Errors**
   - Check if your Typebot server is running
   - Verify the server URL is correct
   - Check for CORS issues if running on different domains

3. **Authentication Errors**
   - Regenerate your API token
   - Verify the token has the correct permissions

### Debug Mode

Check the browser console for detailed error messages. The service includes comprehensive error logging.

## Customization

### Styling

The Typebot chat uses the same styling system as your existing chat:
- Tailwind CSS classes
- Dark/light theme support
- Responsive design

### Message Types

To add support for additional Typebot message types:

1. Update the `TypebotMessage` interface in `src/interfaces/interfaces.ts`
2. Modify the message rendering logic in the chat component
3. Update the `extractTextFromMessages` method if needed

### Input Types

To support additional input types:

1. Update the `TypebotInput` interface
2. Add rendering logic in the chat component
3. Handle the input submission appropriately

## Security Considerations

- Store API tokens securely
- Use environment variables for configuration
- Validate all user inputs
- Implement proper error handling
- Consider rate limiting for API calls

## Development

### Running Locally

1. Copy `.env.example` to `.env`
2. Update the configuration values
3. Start your development server:

```bash
npm run dev
```

4. Navigate to `http://localhost:5173/typebot` to test the integration

### Building for Production

Ensure all environment variables are properly set in your production environment before building:

```bash
npm run build
```

## Support

For issues related to:
- **Typebot API**: Check the official Typebot documentation
- **Integration Code**: Review this documentation and the source code
- **Configuration**: Verify your environment variables and Typebot setup
