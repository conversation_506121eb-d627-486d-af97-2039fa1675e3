import './App.css'
import { Chat } from './pages/chat/chat'
import { TypebotChat } from './pages/chat/typebot-chat'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from './context/ThemeContext'

function App() {
  return (
    <ThemeProvider>
      <Router>
        <div className="w-full h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-white">
          <Routes>
            <Route path="/" element={<Navigate to="/chat" replace />} />
            <Route path="/chat" element={<Chat />} />
            <Route path="/typebot" element={<TypebotChat />} />
          </Routes>
        </div>
      </Router>
    </ThemeProvider>
  )
}

export default App;