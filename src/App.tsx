import './App.css'
import { TypebotChat } from './pages/chat/typebot-chat'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from './context/ThemeContext'

function App() {
  return (
    <ThemeProvider>
      <Router>
        <div className="w-full h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-white">
          <Routes>
            <Route path="/" element={<TypebotChat />} />
            <Route path="*" element={<TypebotChat />} />
          </Routes>
        </div>
      </Router>
    </ThemeProvider>
  )
}

export default App;