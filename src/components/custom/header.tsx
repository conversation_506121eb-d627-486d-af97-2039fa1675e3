import { ThemeToggle } from "./theme-toggle";
import { But<PERSON> } from "@/components/ui/button";
import { useLocation, useNavigate } from "react-router-dom";
import { MessageSquare, Bot } from "lucide-react";

export const Header = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const isTypebotChat = location.pathname === '/typebot';
  const isRegularChat = location.pathname === '/chat';

  return (
    <>
      <header className="flex items-center justify-between px-4 py-2 bg-white dark:bg-gray-900 text-black dark:text-white w-full border-b shadow-sm">
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <img
              src="/images/Merai _ Redesigning Future .png"
              alt="Merai Logo"
              className="h-10"
            />
          </div>

          {/* Navigation buttons */}
          <div className="flex items-center space-x-2">
            <Button
              variant={isRegularChat ? "default" : "ghost"}
              size="sm"
              onClick={() => navigate('/chat')}
              className="flex items-center gap-2"
            >
              <MessageSquare className="h-4 w-4" />
              <span className="hidden sm:inline">Regular Chat</span>
            </Button>
            <Button
              variant={isTypebotChat ? "default" : "ghost"}
              size="sm"
              onClick={() => navigate('/typebot')}
              className="flex items-center gap-2"
            >
              <Bot className="h-4 w-4" />
              <span className="hidden sm:inline">Typebot Chat</span>
            </Button>
          </div>
        </div>

        <div className="flex items-center space-x-1 sm:space-x-2">
          <ThemeToggle />
        </div>
      </header>
    </>
  );
};