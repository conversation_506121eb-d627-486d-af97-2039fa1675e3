import { ThemeToggle } from "./theme-toggle";
import { Bo<PERSON> } from "lucide-react";

export const Header = () => {
  return (
    <>
      <header className="flex items-center justify-between px-4 py-2 bg-white dark:bg-gray-900 text-black dark:text-white w-full border-b shadow-sm">
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <img
              src="/images/Merai _ Redesigning Future .png"
              alt="Merai Logo"
              className="h-10"
            />
          </div>

          {/* Chat indicator */}
          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            <Bot className="h-4 w-4" />
            <span className="hidden sm:inline">AI Assistant</span>
          </div>
        </div>

        <div className="flex items-center space-x-1 sm:space-x-2">
          <ThemeToggle />
        </div>
      </header>
    </>
  );
};