// Typebot Configuration
export const TYPEBOT_CONFIG = {
  // Base URL for Typebot API - can be overridden by environment variables
  BASE_URL: (window as any).ENV?.TYPEBOT_URL || import.meta.env.VITE_TYPEBOT_URL || "http://localhost:7071",

  // Typebot ID - replace with your actual typebot ID
  TYPEBOT_ID: (window as any).ENV?.TYPEBOT_ID || import.meta.env.VITE_TYPEBOT_ID || "qsnfdbamnehg349u88oyabxw",

  // API Token for authentication
  API_TOKEN: (window as any).ENV?.TYPEBOT_TOKEN || import.meta.env.VITE_TYPEBOT_TOKEN || "myAwesomeToken",

  // API endpoints
  ENDPOINTS: {
    START_CHAT: "/api/v1/typebots/{typebotId}/preview/startChat",
    CONTINUE_CHAT: "/api/v1/sessions/{sessionId}/continueChat"
  }
};