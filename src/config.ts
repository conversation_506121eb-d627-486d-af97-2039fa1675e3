// Typebot Configuration for @typebot.io/nextjs component
export const TYPEBOT_CONFIG = {
  // Typebot ID/name - can be overridden by environment variables
  TYPEBOT_ID: (window as any).ENV?.TYPEBOT_ID || import.meta.env.VITE_TYPEBOT_ID || "typebot-merai",

  // API Host URL - can be overridden by environment variables
  API_HOST: (window as any).ENV?.TYPEBOT_URL || import.meta.env.VITE_TYPEBOT_URL || "http://localhost:7071",

  // Component styling - fullscreen
  STYLE: {
    width: "100%",
    height: "100%"
  }
};