export interface Button {
  title: string;
  payload: string;
}

export interface message {
  content: string;
  role: 'user' | 'assistant';
  id: string;
  timestamp: Date;
  isRead: boolean;
  sources?: any[];
  buttons?: Button[]; // Adding buttons property
}

export interface ConversationState {
  state: 'INIT' | 'NAME_PENDING' | 'ORG_PENDING' | 'READY';
  userName?: string;
  organization?: string;
}

export interface ChatResponse {
  response: string;
  sources?: any[];
  conversation_id?: string;
  timestamp?: string;
  session_id?: string;
  state?: string;
}

// Typebot-specific interfaces
export interface TypebotMessage {
  type: 'text' | 'image' | 'video' | 'audio' | 'embed' | 'custom-embed';
  content?: {
    richText?: Array<{
      type: string;
      children: Array<{
        text: string;
        bold?: boolean;
        italic?: boolean;
        underline?: boolean;
      }>;
    }>;
    url?: string;
    html?: string;
  };
}

export interface TypebotInput {
  id: string;
  type: 'text' | 'buttons' | 'email' | 'number' | 'url' | 'phone' | 'date' | 'time' | 'payment' | 'rating' | 'file' | 'picture choice';
  options?: {
    labels?: {
      placeholder?: string;
      button?: string;
    };
    isLong?: boolean;
    isRequired?: boolean;
    items?: Array<{
      id: string;
      content: string;
    }>;
  };
}

export interface TypebotStartChatRequest {
  isStreamEnabled?: boolean;
  message?: {
    type: 'text';
    text: string;
    attachedFileUrls?: string[];
  };
  isOnlyRegistering?: boolean;
  prefilledVariables?: Record<string, string>;
  sessionId?: string;
  textBubbleContentFormat?: 'richText' | 'markdown';
}

export interface TypebotContinueChatRequest {
  message: string;
}

export interface TypebotChatResponse {
  sessionId: string;
  typebot: {
    id: string;
    version: string;
    theme?: any;
  };
  messages: TypebotMessage[];
  lastMessageNewFormat?: string;
  input?: TypebotInput;
  clientSideActions?: Array<{
    type: string;
    [key: string]: any;
  }>;
  logs?: Array<{
    status: string;
    description: string;
    details?: any;
  }>;
  dynamicTheme?: {
    hostAvatarUrl?: string;
    guestAvatarUrl?: string;
  };
  progress?: number;
}
