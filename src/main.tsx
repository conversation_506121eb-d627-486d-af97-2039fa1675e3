import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'

// Import test functions in development mode
if (import.meta.env.DEV) {
  import('./utils/typebotTest').then(({ testTypebotIntegration, checkTypebotConfig }) => {
    console.log('🔧 Typebot test functions loaded. Use in console:');
    console.log('   - testTypebotIntegration() - Full integration test');
    console.log('   - checkTypebotConfig() - Quick config check');
  });
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
