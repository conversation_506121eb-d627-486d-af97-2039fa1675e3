import { useState, useEffect, useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { ChatInput } from '@/components/custom/chatinput';
import { PreviewMessage, ThinkingMessage } from '@/components/custom/message';
import { useScrollToBottom } from '@/components/custom/use-scroll-to-bottom';
import { Header } from '@/components/custom/header';
import { Overview } from '@/components/custom/overview';
import { Button } from '@/components/ui/button';
import { RotateCcw, Settings } from 'lucide-react';
import { typebotApi } from '@/services/typebotApi';
import { message, TypebotChatResponse } from '@/interfaces/interfaces';
import { toast } from 'sonner';

export function TypebotChat() {
  const [messagesContainerRef, messagesEndRef] = useScrollToBottom<HTMLDivElement>();
  const [messages, setMessages] = useState<message[]>([]);
  const [question, setQuestion] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [currentInput, setCurrentInput] = useState<TypebotChatResponse['input'] | null>(null);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);

  // Initialize chat on component mount
  useEffect(() => {
    initializeChat();
  }, []);

  const initializeChat = async () => {
    try {
      setIsLoading(true);

      // Validate configuration first
      const configValidation = typebotApi.validateConfig();
      if (!configValidation.isValid) {
        toast.error(`Configuration Error: ${configValidation.errors.join(', ')}`);
        return;
      }

      const response = await typebotApi.startChat();

      setSessionId(response.sessionId);

      // Process initial messages
      if (response.messages && response.messages.length > 0) {
        const textContent = typebotApi.extractTextFromMessages(response.messages);
        if (textContent) {
          const botMessage: message = {
            content: textContent,
            role: 'assistant',
            id: uuidv4(),
            timestamp: new Date(),
            isRead: false
          };
          setMessages([botMessage]);
        }
      }

      // Set current input if available
      setCurrentInput(response.input || null);
      setIsInitialized(true);

    } catch (error) {
      console.error('Error initializing chat:', error);
      toast.error('Failed to initialize chat. Please check your configuration.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRestart = async () => {
    setMessages([]);
    setSessionId(null);
    setCurrentInput(null);
    setIsInitialized(false);
    setQuestion('');
    await initializeChat();
  };

  const handleSubmit = async (text?: string) => {
    if (isLoading || !sessionId) return;

    const messageText = text || question;
    if (!messageText.trim()) return;

    setIsLoading(true);
    const messageId = uuidv4();

    // Add user message to the chat
    const userMessage: message = {
      content: messageText,
      role: 'user',
      id: messageId,
      timestamp: new Date(),
      isRead: true
    };
    setMessages(prev => [...prev, userMessage]);
    setQuestion('');

    try {
      const response = await typebotApi.continueChat(sessionId, messageText);

      // Process bot response messages
      if (response.messages && response.messages.length > 0) {
        const textContent = typebotApi.extractTextFromMessages(response.messages);
        if (textContent) {
          const botMessage: message = {
            content: textContent,
            role: 'assistant',
            id: uuidv4(),
            timestamp: new Date(),
            isRead: false
          };
          setMessages(prev => [...prev, botMessage]);
        }
      }

      // Update current input
      setCurrentInput(response.input || null);

      // Handle client-side actions if any
      if (response.clientSideActions) {
        response.clientSideActions.forEach(action => {
          console.log('Client-side action:', action);
          // Handle different types of actions here
          // e.g., redirects, script execution, etc.
        });
      }

    } catch (error) {
      console.error('API error:', error);
      const errorMessage: message = {
        content: 'Sorry, there was an error processing your request. Please try again later.',
        role: 'assistant',
        id: uuidv4(),
        timestamp: new Date(),
        isRead: false
      };
      setMessages(prev => [...prev, errorMessage]);
      toast.error('Failed to send message. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleButtonClick = (payload: string, title: string) => {
    setQuestion(title);
    handleSubmit(title);
  };

  const getInputPlaceholder = (): string => {
    if (!isInitialized) return 'Initializing...';
    if (currentInput) {
      return typebotApi.getInputPlaceholder({
        sessionId: sessionId || '',
        typebot: { id: '', version: '' },
        messages: [],
        input: currentInput
      });
    }
    return 'Type your message...';
  };

  const renderButtons = () => {
    if (!currentInput || currentInput.type !== 'buttons') return null;

    const buttonOptions = typebotApi.getButtonOptions({
      sessionId: sessionId || '',
      typebot: { id: '', version: '' },
      messages: [],
      input: currentInput
    });

    if (buttonOptions.length === 0) return null;

    return (
      <div className="flex flex-wrap gap-2 mb-4">
        {buttonOptions.map((option) => (
          <Button
            key={option.id}
            variant="outline"
            size="sm"
            onClick={() => handleButtonClick(option.content, option.content)}
            disabled={isLoading}
          >
            {option.content}
          </Button>
        ))}
      </div>
    );
  };

  return (
    <div className="flex flex-col min-w-0 h-dvh bg-white dark:bg-gray-900">
      <div className="flex justify-between items-center">
        <Header />
        <div className="flex gap-2 mr-4">
          <Button
            onClick={handleRestart}
            variant="ghost"
            size="icon"
            title="Restart conversation"
            disabled={isLoading}
          >
            <RotateCcw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div
        className="flex flex-col min-w-0 gap-1 flex-1 overflow-y-auto pt-2 pb-4 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-700"
        ref={messagesContainerRef}
      >
        {messages.length === 0 && !isLoading && !isInitialized && (
          <Overview onStartChat={initializeChat} isLoading={isLoading} />
        )}

        {messages.length === 0 && !isLoading && isInitialized && (
          <div className="flex flex-col items-center justify-center h-full text-center p-8">
            <h2 className="text-2xl font-semibold mb-4">Ready to Chat!</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Your conversation is ready. Start typing below to begin.
            </p>
          </div>
        )}

        {messages.map((message, index) => {
          const prevMessage = index > 0 ? messages[index - 1] : null;
          const isGrouped = prevMessage && prevMessage.role === message.role;

          return (
            <PreviewMessage
              key={message.id}
              message={message}
              isGrouped={isGrouped}
              showTimestamp={!isGrouped || index === messages.length - 1}
              onButtonClick={handleButtonClick}
            />
          );
        })}

        {isLoading && <ThinkingMessage />}
        <div ref={messagesEndRef} className="shrink-0 min-w-[24px] min-h-[24px]" />
      </div>

      <div className="border-t border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 py-4">
        <div className="flex flex-col mx-auto px-4 gap-2 w-full max-w-3xl">
          {renderButtons()}
          <ChatInput
            question={question}
            setQuestion={setQuestion}
            onSubmit={handleSubmit}
            isLoading={isLoading}
            placeholder={getInputPlaceholder()}
            disabled={!isInitialized || !sessionId}
          />
        </div>
      </div>
    </div>
  );
}
