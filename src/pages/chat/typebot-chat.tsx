import { Standard } from '@typebot.io/nextjs';
import { Header } from '@/components/custom/header';
import { TYPEBOT_CONFIG } from '@/config';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { RotateCcw } from 'lucide-react';

export function TypebotChat() {
  const [key, setKey] = useState<number>(0);

  // Function to restart the chat by remounting the component
  const handleRestart = () => {
    setKey(prev => prev + 1);
  };

  return (
    <div className="flex flex-col h-screen bg-white dark:bg-gray-900">
      {/* Header */}
      <div className="flex justify-between items-center">
        <Header />
        <div className="flex gap-2 mr-4">
          <Button
            onClick={handleRestart}
            variant="ghost"
            size="icon"
            title="Restart conversation"
          >
            <RotateCcw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Typebot Chat Container */}
      <div className="flex-1 w-full">
        <Standard
          key={key} // This will remount the component when key changes
          typebot={TYPEBOT_CONFIG.TYPEBOT_ID}
          apiHost={TYPEBOT_CONFIG.API_HOST}
          style={TYPEBOT_CONFIG.STYLE}
        />
      </div>
    </div>
  );
}
