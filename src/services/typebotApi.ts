import { TYPEBOT_CONFIG } from '../config';
import {
  TypebotStartChatRequest,
  TypebotContinueChatRequest,
  TypebotChatResponse
} from '../interfaces/interfaces';

class TypebotApiService {
  private baseUrl: string;
  private typebotId: string;
  private apiToken: string;

  constructor() {
    this.baseUrl = TYPEBOT_CONFIG.BASE_URL;
    this.typebotId = TYPEBOT_CONFIG.TYPEBOT_ID;
    this.apiToken = TYPEBOT_CONFIG.API_TOKEN;
  }

  private getHeaders(): HeadersInit {
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${this.apiToken}`
    };
  }

  private buildUrl(endpoint: string, params: Record<string, string> = {}): string {
    let url = `${this.baseUrl}${endpoint}`;
    
    // Replace path parameters
    Object.entries(params).forEach(([key, value]) => {
      url = url.replace(`{${key}}`, value);
    });
    
    return url;
  }

  /**
   * Start a new chat session with the typebot
   */
  async startChat(request: TypebotStartChatRequest = {}): Promise<TypebotChatResponse> {
    const url = this.buildUrl(TYPEBOT_CONFIG.ENDPOINTS.START_CHAT, {
      typebotId: this.typebotId
    });

    const defaultRequest: TypebotStartChatRequest = {
      isStreamEnabled: false,
      textBubbleContentFormat: 'richText',
      ...request
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(defaultRequest)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data: TypebotChatResponse = await response.json();
      return data;
    } catch (error) {
      console.error('Error starting chat:', error);
      throw error;
    }
  }

  /**
   * Continue an existing chat session
   */
  async continueChat(sessionId: string, message: string): Promise<TypebotChatResponse> {
    const url = this.buildUrl(TYPEBOT_CONFIG.ENDPOINTS.CONTINUE_CHAT, {
      sessionId
    });

    const request: TypebotContinueChatRequest = {
      message
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data: TypebotChatResponse = await response.json();
      return data;
    } catch (error) {
      console.error('Error continuing chat:', error);
      throw error;
    }
  }

  /**
   * Extract text content from Typebot messages
   */
  extractTextFromMessages(messages: TypebotChatResponse['messages']): string {
    return messages
      .filter(msg => msg.type === 'text')
      .map(msg => {
        if (msg.content?.richText) {
          return msg.content.richText
            .map(block => 
              block.children
                .map(child => child.text)
                .join('')
            )
            .join('\n');
        }
        return '';
      })
      .join('\n')
      .trim();
  }

  /**
   * Check if the response contains input fields
   */
  hasInput(response: TypebotChatResponse): boolean {
    return !!response.input;
  }

  /**
   * Get input placeholder text
   */
  getInputPlaceholder(response: TypebotChatResponse): string {
    if (!response.input?.options?.labels?.placeholder) {
      return 'Type your message...';
    }
    return response.input.options.labels.placeholder;
  }

  /**
   * Get button options from input
   */
  getButtonOptions(response: TypebotChatResponse): Array<{ id: string; content: string }> {
    if (response.input?.type === 'buttons' && response.input.options?.items) {
      return response.input.options.items;
    }
    return [];
  }

  /**
   * Validate configuration
   */
  validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.baseUrl) {
      errors.push('Base URL is required');
    }

    if (!this.typebotId) {
      errors.push('Typebot ID is required');
    }

    if (!this.apiToken) {
      errors.push('API Token is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Export singleton instance
export const typebotApi = new TypebotApiService();
export default TypebotApiService;
