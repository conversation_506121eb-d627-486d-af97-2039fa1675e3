import { typebotApi } from '../services/typebotApi';

/**
 * Test function to verify Typebot API integration
 * This can be called from the browser console for testing
 */
export async function testTypebotIntegration() {
  console.log('🤖 Testing Typebot Integration...');
  
  try {
    // 1. Validate configuration
    console.log('1. Validating configuration...');
    const configValidation = typebotApi.validateConfig();
    
    if (!configValidation.isValid) {
      console.error('❌ Configuration validation failed:', configValidation.errors);
      return false;
    }
    console.log('✅ Configuration is valid');

    // 2. Test starting a chat
    console.log('2. Testing start chat...');
    const startResponse = await typebotApi.startChat({
      message: {
        type: 'text',
        text: 'Hello, this is a test message'
      }
    });
    
    console.log('✅ Start chat successful:', {
      sessionId: startResponse.sessionId,
      messagesCount: startResponse.messages?.length || 0,
      hasInput: !!startResponse.input
    });

    // 3. Test extracting text from messages
    if (startResponse.messages && startResponse.messages.length > 0) {
      console.log('3. Testing message extraction...');
      const extractedText = typebotApi.extractTextFromMessages(startResponse.messages);
      console.log('✅ Extracted text:', extractedText);
    }

    // 4. Test continue chat if we have a session
    if (startResponse.sessionId) {
      console.log('4. Testing continue chat...');
      const continueResponse = await typebotApi.continueChat(
        startResponse.sessionId, 
        'This is a follow-up test message'
      );
      
      console.log('✅ Continue chat successful:', {
        messagesCount: continueResponse.messages?.length || 0,
        hasInput: !!continueResponse.input
      });
    }

    console.log('🎉 All tests passed! Typebot integration is working correctly.');
    return true;

  } catch (error) {
    console.error('❌ Test failed:', error);
    
    // Provide helpful error messages
    if (error instanceof Error) {
      if (error.message.includes('fetch')) {
        console.log('💡 Tip: Make sure your Typebot server is running and accessible');
      } else if (error.message.includes('401') || error.message.includes('403')) {
        console.log('💡 Tip: Check your API token - it might be invalid or expired');
      } else if (error.message.includes('404')) {
        console.log('💡 Tip: Check your Typebot ID - it might be incorrect');
      }
    }
    
    return false;
  }
}

/**
 * Quick configuration check
 */
export function checkTypebotConfig() {
  console.log('🔧 Checking Typebot Configuration...');
  
  const validation = typebotApi.validateConfig();
  
  if (validation.isValid) {
    console.log('✅ Configuration is valid');
    console.log('📋 Current config:', {
      baseUrl: process.env.VITE_TYPEBOT_URL || 'http://localhost:7071',
      typebotId: process.env.VITE_TYPEBOT_ID || 'qsnfdbamnehg349u88oyabxw',
      hasToken: !!(process.env.VITE_TYPEBOT_TOKEN || 'myAwesomeToken')
    });
  } else {
    console.error('❌ Configuration errors:', validation.errors);
    console.log('💡 Make sure to set these environment variables:');
    console.log('   - VITE_TYPEBOT_URL');
    console.log('   - VITE_TYPEBOT_ID');
    console.log('   - VITE_TYPEBOT_TOKEN');
  }
  
  return validation.isValid;
}

// Make functions available globally for testing
if (typeof window !== 'undefined') {
  (window as any).testTypebotIntegration = testTypebotIntegration;
  (window as any).checkTypebotConfig = checkTypebotConfig;
}
