{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b --noEmit false --skipLibCheck true || true && vite build", "serve": "serve dist -p 8501", "lint": "eslint .", "preview": "vite preview", "prepare": "husky"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slot": "^1.1.0", "@tailwindcss/typography": "^0.5.15", "@typebot.io/nextjs": "^0.6.0", "class-variance-authority": "^0.7.0", "classix": "^2.2.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cx": "^24.10.11", "framer-motion": "^11.11.13", "geist": "^1.3.1", "langfuse": "^3.29.1", "lucide-react": "^0.456.0", "path": "^0.12.7", "react": "^18.3.1", "react-dom": "^18.3.1", "react-intersection-observer": "^9.16.0", "react-markdown": "^9.0.1", "react-router-dom": "^6.28.0", "remark-gfm": "^4.0.0", "socket.io-client": "^4.8.1", "sonner": "^1.7.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.0", "uuid": "^11.0.3"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/node": "^20.0.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "husky": "^9.1.7", "postcss": "^8.4.49", "serve": "^14.2.4", "tailwindcss": "^3.4.14", "typescript": "~5.6.2", "typescript-eslint": "^8.11.0", "vite": "^5.4.10"}}